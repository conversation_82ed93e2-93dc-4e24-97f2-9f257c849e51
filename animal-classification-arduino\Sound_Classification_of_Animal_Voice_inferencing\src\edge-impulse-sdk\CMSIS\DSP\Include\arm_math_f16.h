/******************************************************************************
 * @file     arm_math_f16.h
 * @brief    Public header file for f16 function of the CMSIS DSP Library
 * @version  V1.10.0
 * @date     08 July 2021
 * Target Processor: Cortex-M and Cortex-A cores
 ******************************************************************************/
/*
 * Copyright (c) 2010-2021 Arm Limited or its affiliates. All rights reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the License); you may
 * not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an AS IS BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef _ARM_MATH_F16_H
#define _ARM_MATH_F16_H

#include "edge-impulse-sdk/CMSIS/DSP/Include/arm_math.h"

#ifdef   __cplusplus
extern "C"
{
#endif

#include "edge-impulse-sdk/CMSIS/DSP/Include/arm_math_types_f16.h"
#include "edge-impulse-sdk/CMSIS/DSP/Include/dsp/none.h"
#include "edge-impulse-sdk/CMSIS/DSP/Include/dsp/utils.h"
#include "edge-impulse-sdk/CMSIS/DSP/Include/dsp/basic_math_functions_f16.h"
#include "edge-impulse-sdk/CMSIS/DSP/Include/dsp/interpolation_functions_f16.h"
#include "edge-impulse-sdk/CMSIS/DSP/Include/dsp/bayes_functions_f16.h"
#include "edge-impulse-sdk/CMSIS/DSP/Include/dsp/matrix_functions_f16.h"
#include "edge-impulse-sdk/CMSIS/DSP/Include/dsp/complex_math_functions_f16.h"
#include "edge-impulse-sdk/CMSIS/DSP/Include/dsp/statistics_functions_f16.h"
#include "edge-impulse-sdk/CMSIS/DSP/Include/dsp/controller_functions_f16.h"
#include "edge-impulse-sdk/CMSIS/DSP/Include/dsp/support_functions_f16.h"
#include "edge-impulse-sdk/CMSIS/DSP/Include/dsp/distance_functions_f16.h"
#include "edge-impulse-sdk/CMSIS/DSP/Include/dsp/svm_functions_f16.h"
#include "edge-impulse-sdk/CMSIS/DSP/Include/dsp/fast_math_functions_f16.h"
#include "edge-impulse-sdk/CMSIS/DSP/Include/dsp/transform_functions_f16.h"
#include "edge-impulse-sdk/CMSIS/DSP/Include/dsp/filtering_functions_f16.h"

#ifdef   __cplusplus
}
#endif

#endif /* _ARM_MATH_F16_H */


