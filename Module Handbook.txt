




















MODULE HANDBOOK 2024/25
Edge and Embedded Intelligence
Module Code: COM683 (65626)


Programme(s):	BSc (Hons) Computing Science
BSc (Hons) Software Engineering


School of Computing
Faculty of Computing, Engineering and the Built Environment



Dr. <PERSON>
Module Coordinator

 Room Number
BC_04_131

 Email
i.cle<PERSON>@ulster.ac.uk

 Phone Number
02895 365087

 Office Hours
Monday- Friday; 9am - 5pm.

Notice
Please be aware the information provided within the module handbook is subject to change.




Table of Contents


Welcome	3
1. Module Overview and Communication	4
2. Sustainable Development Goals (SDGs) and Graduate Attributes (GAs) in your module	3
3. Aim and Learning Outcomes	5
4. Assessment and Feedback	6
5. Learning Resources	6
6. Organisation and Management	18
7. Student Voice and Support Services	21

Welcome


Welcome


Welcome to this final year optional module on Edge and Embedded Intelligence! My name is <PERSON>, I am a Senior Lecturer within the School of Computing, and I am the module co-ordinator form COM683.
Embedded Machine Learning is a cutting-edge field that brings the power of Artificial Intelligence (AI) and specifically machine learning (ML) to computationally and power constrained domain of embedded systems.
This is an introductory course at the intersection of ML and Embedded IoT Devices which covers machine learning applications and algorithms using embedded hardware, sensors, actuators, and software.
Embedding machine learning in a device at the extreme end point - right at the data source - is fundamentally different from traditional data-centred ML. Embedded ML is all about real-time processing of time-series data that comes directly from sensors. By the end of this course, you will collect and preprocess data to build a dataset, design a model, train a model, evaluate and optimise the pipeline, convert the model to run on hardware, deploy the model on a microcontroller, make inference and solutions to address real world problems.
This will enable future applications development across medical devices, home appliances, industrial automation, wild-life conservation, smart agriculture and many more.
I hope you find the module interesting and useful in your future carer.


Kind regards


Dr. Ian Cleland
Module Coordinator


1. Module Overview and Communication


Module Details

Module Title
Edge and Embedded Intelligence
Module Code
COM683
Module Level
6
Credit points
20
Module Status
Optional
Semester
2
Location
Belfast Campus
Delivery Mode
Face-to-face
Contact Details and Communication Methods
Module Coordinator

Dr. Ian Cleland - <EMAIL>
Teaching Staff Contact Details
Dr. Idongesit (ID) Ekerete - <EMAIL> Prof. Chris Nugent - <EMAIL>

General Information, Queries and Consultations


If you require advice on any aspect of the module, please read the guidance provided here, in the Module Handbook.
For Specific Queries please contact Dr. Ian Cleland if you have questions relating to the module or wish to schedule an appointment. Please note, we aim to respond to emails within 48 hours during the working week.



Module Announcements
Key announcements will be presented during tutor-led activities.
Out of class communication including notifications, reminders, etc will be distributed via the Blackboard Announcement tool. You will receive a duplication of the announcement direct to your student email inbox. It is essential that you check your emails regularly.
It also advisable that you download the 'Blackboard' App as an alternative to access these announcements, notifications, and reminders as well as content.






2. Sustainable Development Goals (SDGs) and Graduate Attributes (GAs) in your module




Sustainable Development Goals (SDGs)
The UN SDGs are a comprehensive set of global goals to end poverty, protect our planet, and improve living conditions of the Global Population. You are encouraged to think critically and reflect on SDGs in the context of this module.
The following SDGs are relevant in the context of this module:



Graduate Attributes (GAs)

Ulster University's Graduate Attributes are a high-level set of competencies, developing universal understandings, skills, qualities, and values.
As a result of engaging with this module you will have opportunities to develop and practise the following selected attributes:



Thriving Individual

Engaged Learner
Enhancing Potential	

Subject Specialist	
Confidence and Resilience	

Creative Problem Solver	
Well-being

Researcher	
Growth Mindset	

Critical Thinker	



Collaborative Professional

Active Citizen
Responsible Team Player

Citizen with Integrity	
Effective Communicator	

Inclusive Citizen
Enterprising Innovator	

Sustainable Citizen	
Digital Fluent Professional	

Future Thinker	


3. Aim and Learning Outcomes




Module Rationale
Edge and embedded intelligence are at the intersection of embedded Machine Learning (ML) applications, algorithms, hardware, and software. It differs from other applications of machine learning (e.g., server and cloud) in that it requires not only expertise in software, but also knowledge of embedded hardware. The module will focus on the basics of machine learning and deep learning, specifically for deployment in and embedded devices and systems, including microcontrollers and other constrained devices. Throughout the module, students will learn data science techniques for collecting data from sensor devices and develop an understanding of learning algorithms to train basic machine learning models which are optimised to run on resource constrained devices. At the end of this module, successful students will have a detailed understanding the principles behind embedded intelligence and the ability to develop and evaluate edge and embedded intelligence applications. This is a growing area for both industry and academic research. The module is aligned well with the research interest of the Pervasive Computing Research Centre, within the School of Computing.
Overall Aim of the Module
The aim of the module is to:
* Provide an opportunity to carry out a significant investigation of the technologies used in edge and embedded intelligence applications.
* To provide students with the practical experience of designing, developing evaluating and optimising solutions for edge and embedded intelligence applications.
* Deliver an understanding of principles of designing responsible AI solutions for Edge and Embedded applications across a range of application domains.
Learning Outcomes


Successful participants will be able to:
1) Effectively analyse the cutting-edge concepts, principles, theories, and practices underpinning edge and embedded Intelligence.
2) Critically evaluate the ethical, social, and professional considerations associated with the design and implementation of edge and embedded intelligence solutions.
3) Apply practical and research skills with which to design, develop, deploy, critically evaluate, and optimise machine learning models on embedded devices.
4) Conceive and develop a (novel) Embedded Intelligence application running on a constrained device that addresses a real-world problem.


4. Assessment and Feedback




This module will be assessed via two items of assessment

Type

Assessment method and submission date:

Percentage (%)

Feedback due date

Coursework 1
Video Presentation (Project Pitch)
12:00pm, Noon, on Week 8, Tuesday 18th March 2025

40%

20 working days after submission.



Coursework 2
Project (Presentation and Demo)
Submission: 12:00pm, Noon, on Week 11, Friday 11th April 2025
Presentation: Week 12 Schedule to be confirmed.



60%


20 working days after submission.

Assessment Guidance
1) All CW components are individual assignment pieces and all work submitted and assessed must be your own. Plagiarism will not be tolerated and will be dealt with according to University policy.
2) Your work should include references to relevant journal articles and other good quality information sources and should be properly laid out using the Harvard system of referencing. More detailed guidance is available in the 'Guide to Referencing in the Harvard Style'. We encourage you to make good use of all the support services offered by your Faculty Subject Librarian, further details are in the Learning Support Services Section.
3) You should refer to the assessment criteria to provide fuller details of the marking criteria for each classification band.
4) In addition, you should refer to the standard assessment guidelines as presented in your Course Handbook/Support Area, this includes guidance and policies on referencing style, plagiarism, etc.
5) Coursework must be submitted by the dates specified. Coursework submitted after the deadline, without prior approval, is not normally accepted. For further guidance on the late submission of coursework, please contact your Course Director or Year Tutor.
6) Non-compliance with the time limit for presentations/ video submission will result in a penalty being applied in accordance with the University Word Limit Policy. Details of this are found below.
7) Aligning with the University guidance on the use of AI for students, AI may be reasonably used within the assignments of this module, this includes planning the structure of video/ presentation and developing creative ideas/ inspiration.




Coursework Overview
The module assessment comprises of one Project consisting of two components:
1) Coursework 1: Research Project Proposal (Video Presentation) [40%]
2) Coursework 2: Implementation (Presentation and Demo) [60%]
You will work individually to design and implement an embedded intelligence solution to address a real- world problem. The solution should be aligned to address challenges within one of the 17 UN Sustainable Development Goals (SDG). Some examples of such challenges are provided below; however it is up to you to decide on a suitable topic:
1) Detecting gestures for rehabilitation or exercise.
2) Monitor animals for wildlife conservation.
3) Tracking people for security and safety applications.
4) Anomaly detection in industrial predictive maintenance.
5) Automatic recognition of plant disease in smart farming.
6) Recognising sounds, like snoring or coughing for health.
7) Low cost, maintenance free weather station.
More inspiration can be found at the AI for Good TinyML Challenge. Detailed assessment guidance will be given in the Week 4 Lecture and aims to provide advice on the use of media/technologies for this assessment exercise.
Coursework 1
Video Presentation: Project Pitch [40%]
For coursework 1, and to ensure that an appropriate project is chosen, you must individually identify and present a project pitch/ proposal in the form of a 5-minute video presentation.
For the project pitch you should undertake a literature review on the selected challenge. This literature review will inform project methodology and should focus on the project topic area and how others have used embedded machine learning and
IoT to address the problem.
What is the format?
You are required to present your project pitch within a 5-minute video presentation. You can use any material you like during the 5-minute video. This could be any number of slides, video, images or other media. Additionally, you should attach up to 3 PowerPoint slides (that include a 2 second delay between slides), which present the list of referenced sources of evidence used in the preparation of your project pitch. You must physically appear in the video and the video must be delivered in your own voice.
The Video should be no longer than 5 minutes in length (excluding the reference slides) and should be encoded to .mp4 format. Any items exceeding the time limits specified will have proportion of marks deducted in line with Ulster University Guidelines.


Penalties for exceeding the assessment limits
Where submitted work exceeds the agreed assessment limit, a margin of up to +10% (30 seconds) of the work limit will be allowed without any penalty of mark deduction.
If the work submitted is significantly in excess of the specified limit (+10%), there is no expectation that staff will assess the piece beyond the limit or provide feedback on work beyond this point. Markers will indicate the point at which the limit is reached and where they have stopped marking.
A mark will be awarded only for the content submitted up to this point. No additional deduction or penalty will be applied to the overall mark awarded. The student is self-penalising as work will not be considered/marked. Disciplinary procedures due to Academic Misconduct may be invoked if the word count/workload has been deliberately and significantly falsified.


What is included in the Video?
During the video you must discuss the following aspects;
1) An overview of the problem being addressed, including the scale and importance of the problem, supported with relevant literature and statistics (10%).
2) Review of existing research in this area including the type of sensors used, what machine learning has been applied, what results have been achieved and any challenges/limitations of the current approaches (15%).
3) A description of your proposed solution, including description of the sensors and other hardware to be used (20%).
4) A high-level description of the Machine learning approach to be used and the data to be collected (20%).
5) A discussion of the ethical, social, and technical challenges associated with the solution (15%).
6) A list of papers or other resources they intend to use to inform the project. This should typically include 10-15 sources of evidence from peer reviewed journal or conference papers. (10%).
10% of the marks will be attributed to the quality of the presentation, including the structure and general delivery of the content.
When is it due?


How do I submit?
The video (.mp4) should be uploaded onto Blackboard Course Area, through the Panopto Media Assignment Dropbox, by the specified date and time outlined in the Key Assessment Information Table above. You should check that the submission can be opened as corrupted files will be treated as a non- submission. You should keep a receipt (screenshot) of the confirmation provided by BBL as proof of submission. All submitted assignments should have the file name: "SurnameFirstNameBNumber" (e.g. BrownJohnB00001234_Assignmen1).


Feedback
Written Feedback on the assessment will be provided, as per University Guidance, within 20 working days from the submission date. Feedback will be provided via BlackBoard or FAN/ Email. You will be given an opportunity to reflect and act on this feedback bringing improvements and learning into the subsequent presentation.







Coursework 1 - ASSESSMENT RUBRIC/MARKING PROFORMA

Criteria (100%)
0-39%
fail
40-49%
3rd
50-59%
2.2
60-69%
2.1
70-79%
1st
80-100%
High 1st
Problem Overview

10%
Demonstrates a very limited ability in identifying a problem statement or related contextual factors.
Demonstrates a limited ability in identifying a problem statement or related contextual factors.
Begins to demonstrate the ability to construct a problem statement with evidence of most relevant contextual factors, but problem statement is
superficial.
Demonstrates the ability to construct a problem statement with evidence of most relevant contextual factors, and problem statement is
adequately detailed.
Demonstrates the ability to construct a clear and insightful problem statement with evidence of the majority of relevant contextual factors
described.
Demonstrates outstanding ability to construct a clear and insightful problem statement with evidence of all relevant contextual factors. No weaknesses
Existing Research

15%
None/ very limited Evaluation of existing research covering only 1 or 2 examples. The review does not examine the pros
and cons of the approaches taken.
Evaluation of existing research is superficial covering a limited number of sources. Pros and cons of existing approaches is limited.
Evaluation of solutions is good but brief. There is some identification of challenges/ limitations. These are clearly articulated but could be expanded upon.
Informed evaluation of existing research.
Thorough coverage of challenges/ limitations.
Evaluation of existing research is deep and elegant; Extensive coverage of challenges/ limitations.
Outstanding evaluation of existing research which is deep and elegant; Extensive coverage of challenges/ limitation highlighting potential for innovation.
Description of Solution: Sensors/ Hardware 20%
No/ very limited description of hardware/ sensors to be used. Key details of the description are perhaps confused/ muddled. No
justification provided.
Description of hardware/ sensors to be used is adequate. However, key details about how they will be used is limited.
Limited justification provided.
Clear description of sensors/ hardware. With solid plan for how they will be used.
Sound justification for their selection provided.
Thorough description of sensors/ hardware. With detailed plan for how they will be used. Informed justification for their selection provided.
Extensive description of sensors/ hardware. With detailed plan for how they will be used. Informed justification for their selection covering all relevant aspects.
Outstanding description of sensors/ hardware. With detailed plan for how they will be used. Informed justification for their selection covering all relevant aspects.
Description
demonstrates elements of innovation/ creativity.
Description of Data collection, processing, and ML.
20%
None/ very limited description of data collection, processing, and machine learning approach.
Demonstrates a lack
Adequate description of data collection, processing, and machine learning approach.
Demonstrates a
Good description of data collection, processing, and machine learning approach.
Demonstrating a
Thorough description of data collection, processing, and machine learning approach.
Demonstrating a sound understanding of the
Extensive description of data collection, processing, and machine learning approach.
Demonstrating a excellent
Outstanding description of data collection, processing, and machine learning approach.
Demonstrating a excellent understanding








of understanding of the main steps.
limited of understanding of the main steps.
sound understanding of the main steps.
main steps. Highly informed about all aspects.
understanding of the main steps. Highly informed about all
aspects.
of the main steps. Approach demonstrates an innovative/ creative
approach.
Ethical, social and technical challenges 15%
No/	very	limited discussion		of
technical,	social
and	ethical challenges		not
considered	in sufficient detail.
Satisfactory discussion			of ethical, social and technical challenges, perhaps			focusing upon	a		subset whilst		neglecting others.
Good	coverage	of challenges, presenting a number of examples relating to	social,	practical and technical issues. Might include some brief discussion of the
impact of these on the future research.
Very Good coverage of	challenges, presenting a number of examples relating to social, practical and technical issues. Might include some brief discussion of the
impact of these on the future research.
Excellent discussion of challenges including literature informed analysis of practical, social and technical challenges.	The impact of these on the future of the sector is formally critiqued.
Outstanding discussion of challenges including literature informed analysis of practical, social and technical challenges. The impact of these on the future of the sector is critiqued in exceptional detail.
Quality of References

10%
None/ Insufficient referencing. Almost exclusively   from
informal	web resources/ lecture slides.
Based mainly on informal	web resources / lecture slides.
Relevant sources but perhaps largely from conferences or with inaccurate formatting.
References appropriate			& formatted	according to	the		formal
specified. Recent conferences citation included with scope to include further journal
citations.
References accurately formatted,		citing		a range of publications, both current and past, sourced	from	both established conference			and journal forums.
References accurately formatted, citing a range of publications, both current and past, sourced from both established conference and journal forums. References  used  to
justify all main points.
Presentation 10%
Audio	only	/		or slides			only
/inaudible. Cannot understand presentation because there is no sequence	of information
Talking over slides
/ pace that was too fast or too slow. Difficulty following presentation because sequence is illogical.
Pace and clarity good. A mix of slides/ video/ images. Some further dynamic elements would have enhanced Presents information in mostly logical sequence which audience can follow.
Pace and clarity very good. A mix of slides/ video/ images. Some further	dynamic elements would have enhanced the overall vodcast. Presents information in logical sequence which is easily followed.
A clear, concise and professional presentation	that incorporated a range of dynamic content to enhance the overall presentation.
Presents information in logical, interesting sequence which is easily followed.
Outstanding presentation			that incorporated a range of creative			dynamic content to enhance the overall	presentation. Presents information in logical,		interesting sequence		 which		is easily followed follow.






Coursework 2
Project Presentation and Demo [60%]
For coursework 2, you are required to implement the solution outlined in the project proposal. Projects must be carried out on real hardware and software testbeds. Components of the assessment will require collection, processing and visualisation of data, development and implementation of machine learning algorithms, a critical evaluation of its performance and presentation of the findings. Projects must include elements of both embedded systems and machine learning.
What is required?
You will produce a portfolio of evidence (code, dataset, presentation slides and a video demonstration), which will be submitted during week 12. The portfolio will be presented in the form of an individual oral presentation (approx. 12-minutes) presented during an oral defence of the project to module tutors. The presentation will be followed by 5-10 minutes for questions. The presentation will describe the portfolio of evidence emerging from the project which should include background research, methodology, code segments and description of relevant/collected datasets. Consideration will be given to the students understanding of theoretical knowledge, level of understanding of the work undertaken and the novelty/ innovation of the approach.
Presentations will be scheduled after the date of the submission at a time to be confirmed by the module co-ordinator. Informal verbal feedback will be provided immediately on conclusion of the presentation with summative feedback in the form of a grade and comments being provided in accordance with University guidance. Students will be expected to be self-motivated to learn the technologies and prepare a presentation to describe their findings.
Please note that you may work with others to collect and share data for use within the project, however the implementation of the solution and presentation must be completed individually. Where data has been shared by or received from another student this must be indicated during the data collection section of the presentation.
What should the presentation include?
In preparing to present the portfolio for the project, you should prepare a presentation which includes the following aspects. Please note that number of slides are included as indicative.
1) 1 slide: Brief Summary of the specific problem you are trying to address with your solution (5%).
2) 2 slides: Description of the solution including how it specifically addresses the identified problem. This should draw on relevant literature and consider contextual factors such as ethical technical and social aspects (10%).
3) 2 slides: Details of the methodology followed to collect the dataset, including the steps taken to ensure high quality data was collected and labelled (15%).
4) 3 slides: Details of implementation of machine learning, including preprocessing, feature extraction, modelling approaches, validation methodology (20%).
5) 3 slides: Critical evaluation of the performance of the solution, including deep analysis of the machine learning results, both off and online. This should compare different methodological approaches. It should also identify avenues for future improvement (20%).
6) 1 slide: Demonstration of solution. The developed solution should be demonstrated to show its performance. This can be via video or live during the presentation (10%).







There will be 10% reserved for your understanding of theoretical knowledge of what you have done during the project. Additionally, 10% of the marks will be attributed to the quality of the presentation, including the structure and general delivery of the content.


When is it due?



What should be submitted?
The following components MUST be uploaded onto the relevant submission area on Blackboard. There is one submission area for each component. All components are compulsory.
1) A copy of any code, with comments, produced for the project (i.e. .zip file containing Arduino library and link to Public Edge Impulse Project).
2) A copy of the dataset collected and used to train the machine learning algorithms (i.e. .zip file containing test and train samples).
3) Presentation slides to be used during the oral presentation (i.e. slides in .ppt or .pdf format).
4) Short video (approx. 1 minute) providing a demonstration of the developed solution working (i.e. a split screen video showing the sensor/hardware responding in the desired way to an input stimulus) submitted through Panopto Media Assignment Dropbox.
You should check that the submission can be opened as corrupted files will be treated as a non- submission. You should keep a receipt (screenshot) of the confirmation provided by Blackboard as proof of submission. All submitted assignments should have the file name: "SurnameFirstNameBNumber_Component" (e.g. BrownJohnB00001234_Code).


Feedback
Informal verbal feedback will be provided immediately on conclusion of the presentation. Written Feedback on the assessment will be provided, as per University Guidance, within 20 working days from the submission date. Feedback will be provided via BlackBoard or FAN/ Email







Coursework 2 - ASSESSMENT RUBRIC

Criteria (100%)
0-39%
fail
40-49%
3rd
50-59%
2.2
60-69%
2.1
70-79%
1st
80-100%
High 1st
Problem Overview

5%
Problem to be considered critically is stated without clarifications or description.
Problem to be considered critically is stated but description leaves some terms undefined, ambiguities, unexplored, boundaries undetermined, and backgrounds unknown.
Problem			to			be considered critically is stated but description leaves	some	minor terms		undefined, ambiguities, unexplored, boundaries undetermined,		and
backgrounds unknown.
Issue/problem to be considered critically is stated, described, and clarified so that understanding is not seriously impeded by omissions.
Issue/problem		to		be considered			critically			is stated	clearly			and described comprehensively, delivering		all		relevant information necessary for full understanding.
Issue/problem to be considered critically is stated clearly and described comprehensively, delivering all relevant information necessary for full understanding. Opportunities for innovation are highlighted.
Description	of Solution

10%
Proposes				a solution that is difficult			to evaluate because	it		 is vague or only indirectly addresses		the problem statement.
Proposes a generic solution rather than one that is individually designed to address the specific contextual factors of the problem.
Proposes a solution that indicates comprehension of the problem. Selection of chosen solution is not well justified. Solution is sensitive to some of the contextual factors such as: ethical, technical, or social dimensions.
Proposes a solution that indicates comprehension of the problem.				Justifies selection		of			chosen solution		but			doesn't draw	on		literature. Solution is sensitive to contextual factors such
as: ethical, technical, or social dimensions.
Proposes a solution that indicates a deep comprehension of the problem. Justification of selected solution drawing on relevant literature. Solution is sensitive to contextual factors as well as all the contextual factors:
ethical, technical, and social dimensions.
Proposes a solution that indicates a deep comprehension of the problem. Justification of selected solution drawing on relevant literature, highlighting the innovative nature of the approach. Solution is sensitive to contextual factors as well as all the contextual factors: ethical,
technical, and social dimensions of the problem.
Methodology for Data Collection

15%
No methodology followed.	Data collection is not structured. Data collected		is insufficient/ incomplete.
Methodology	is adequate but perhaps missing some minor elements/ is incomplete.
Solid methodology in place which provides good quality data on which to develop and evaluate the solution. Most elements of the methodology	are explained and justified
Thorough methodology in place which provides high quality data on which to develop and evaluate the solution. All elements of the methodology	are explained and justified
Fully detailed, excellent methodology in place which provides ample high-quality data on which to develop and evaluate the solution. All elements of the methodology are explained and justified drawing on appropriate	literature/ experimentation.
Fully detailed, outstanding methodology in place which provides ample high-quality data on which to develop and evaluate the solution. All elements of the methodology are explained and justified drawing on appropriate literature/ experimentation. Methods used demonstrate	creativity/
innovation in the approach.
Development/ Implementation of	Machine Learning
20%
No evidence of machine learning	being implemented.
Implementation of one machine	learning approach with no justification for its selection.
Implementation of a number of suitable machine	learning approaches. Good justification for their selection.    Perhaps
some misunderstanding
Implementation of a number of suitable machine	learning
approaches. Strong justification for their selection.    Perhaps
some	minor
Implementation of a number of suitable machine learning	approaches. Justification for their selection underpinned by relevant		literature/
experimentation.	Full
Implementation of a number of suitable machine learning approaches. Full justification for their selection underpinned by relevant	literature/ experimentation.		Full
understanding	about










about	deployment/ implementation.
misunderstanding about deployment/
implementation.
understanding	about deployment/
implementation.
deployment/	implementation with no weaknesses.
Critical Evaluation	of Performance 20%
Evaluation   of
solution	is superficial.
Reviews results superficially in terms of the problem defined with	no
consideration of need for further
work.
Evaluation of solution is brief. Reviews results in terms of the problem defined with little, if any, consideration of need for further work.
Evaluation of solution is adequate. Reviews results relative to the problem defined with some consideration of need for further work.
Thorough evaluation of solution.	Reviews results relative to the problem defined with informed consideration of need for further work.
Evaluation of solutions is deep and elegant and includes, deeply and extensively, all aspects of performance. Reviews results relative to the problem defined with extensive,	specific considerations of need for further work.
Evaluation of solutions is deep and elegant and includes, deeply and extensively, all aspects of performance. Reviews results relative to the problem defined with extensive, specific considerations of need for further work. Results demonstrate the innovative solution   has   advanced
compared to related works.
Understanding of	Theoretical Knowledge
10%
Very		limited understanding of key	concepts. Unable				to answer questions about the			work
produced.
Limited understanding of key concepts. and can answer only rudimentary questions.
Good understanding of key concepts and can answer most questions but fails to elaborate.
Very	good
understanding of key concepts and can answer all questions but fails to elaborate.
Student demonstrates full knowledge and answers all questions with explanations and elaboration.
Student demonstrates full knowledge and answers all questions with explanations and elaboration. Demonstrates an ability to use this knowledge to identify opportunities for innovation/ creativity.
Demonstration of Solution 10%
Unable	 to/ insufficient demonstration of developed solution.	No appreciation for how the solution functions		or
could	be
improved.
Satisfactory demonstration					of developed		solution. Limited appreciation for how	the			solution functions.				Limited understanding of the limitation and how the solution		may		be
improved.
Good demonstration of the developed solution. With sound appreciation of how the solution functions.	Some understanding of the limitation and how the solution may be improved.
Very	good
demonstration of the developed solution. Demo showcases all aspects of how the solution functions. Informed understanding of the limitation and how
the solution may be improved.
Excellent demonstration of the developed solution. The demo showcases all aspects of how the solution functions.	Extensive understanding of the limitation and how the solution may be improved.
Outstanding demonstration of the developed solution. Showcasing all aspects in a creative way. Extensive understanding of the limitation and how the solution may be improved.
Organisation and Coherence 10%
Poor presentation. Cannot understand presentation because there is no sequence of information.
Talking over slides / pace that was too fast or too slow. Difficulty following presentation because sequence is illogical.
Pace and clarity good. A mix of slides/ video/ images. Some further dynamic elements would have enhanced. Presents information in logical sequence which can be followed.
Pace and clarity very good. A mix of slides/ video/ images. Some further	dynamic elements would have enhanced the overall vodcast. Presents information in logical sequence which is easily followed.
A clear, concise, and professional presentation that incorporated a range of dynamic content to enhance the overall presentation. Presents information in logical, interesting sequence which is easily followed.
Outstanding presentation that incorporated a range of creative dynamic content to enhance    the    overall
presentation.	Presents information in logical, interesting sequence which is easily followed follow.


5. Learning Resources



  A list of current learning resources specifically chosen to build your knowledge and understanding for this module
Reading List
 Also available online via Key Links: https://ulster.keylinks.org/#/
Books/Journal Article/Publications Required Reading (Must read)
* Iodice, G.M., (2023). TinyML Cookbook: Combine artificial intelligence and ultra-low-power embedded devices to make the world smarter (Second Edition). Birmingham, UK: Packt Publishing
* Warden, P. and Situnayake, D., (2019). TinyML: Machine Learning with Tensorflow Lite on Arduino and Ultra-Low-Power Microcontrollers. Sebastopol, California, USA: O'Reilly Media.


Books/Journal Article/Publications Recommended Reading (Should read/Could read)
* Buyya, R. and Srirama, S.N. eds., (2019). Fog and edge computing: principles and paradigms. Hoboken, NJ, USA: John Wiley & Sons.
* Situnayake, D. and Plunkett, J., (2022), AI at the Edge. O'Reilly Media, Sebastopol, California, USA: Incorporated.


Useful Journals
* Pervasive and Mobile Computing: https://www.sciencedirect.com/journal/pervasive-and-mobile-computing
* IEEE Pervasive Computing: https://ieeexplore.ieee.org/xpl/RecentIssue.jsp?punumber=7756
* Personal and Ubiquitous Computing: https://link.springer.com/journal/779


Useful Library Databases and Websites
* Arduino: https://www.arduino.cc/
* TinyML Foundation: https://www.tinyml.org/
* EdgeImpulse: https://edgeimpulse.com/






Library's Support Services
In collaboration with teaching staff, your Campus Librarian will be delivering timetabled workshops to ensure you are able to make the best use of the Library's services and information resources in successfully completing your coursework. Furthermore, if you need specific help with an assignment or dissertation then you can make an appointment to meet with your Subject Librarian by email, phone or through using
the appointments schedule.
Online support is also available by accessing the Library's Guides as these provide help in developing your information and research skills by identifying the best learning resources available, forming effective literature searches, offering academic writing support and adopting the best referencing techniques.


Blackboard Learning Support
If you require help or support with any of the digital learning tools utilised within your module, please contact The Blackboard Helpdesk via:
 028 9536 7188, or by  e-<NAME_EMAIL>


6. Organisation and Management



COM683 Edge and Embedded Intelligence, is a 20-credit point module, this requires approximately 200 hours of your commitment, distributed through the following learning and teaching activities over the 15-week semester. For a description of the nature of the learning activities please refer to your course handbook.


Summary of Learning Activities

Learning Activities: Week 1-12
Indicative Weekly Hours
Total Hours (200 hours)
Lecture and Class Activities
2 hours
22
Seminar or Tutorial Activities
1 hour
11
Practical Lab Classes
2 hours
22
Independent Study: Week 1-12
Indicative Guide

Assigned Reading and Note-taking
3 hours
36
Assessment Activities and Seminar Preparation
9 hours
108

The teaching and learning plan provides a more detailed overview of content on a weekly basis.
Module delivery will typically consist of a two-hour lecture and a one-hour Seminar although format may vary slightly between weeks/sessions.


 For the day, time and room number view your Timetable via PUBLISH.


Seminars - please refer to the teaching and learning plan below for the seminar topic. Attendance at seminars is most important and therefore will be monitored. All students should download the SEAtS Student Attendance App to their smartphones. Regularly checking into class using the app helps you keep track of your attendance and ensures you stay on track with your studies. Please view the university attendance website for more information including user guides, video demonstrations, and FAQs.







Teaching and Learning Plan
The teaching plan/order of weekly topics is subject to change. The list below provides an outline summary of weekly activities and further information is available on BBL in the relevant week's folder. Students will be expected to engage in all prescribed activities that contribute towards final assessment.

Week No.

Topic

Lecture (2 Hr)

Tutorial (1hr)

Practical (2hr)

Deliverables

Week1
Introduction and Foundations

Edge Intelligence Fundamentals

Hardware/ Software introduction
Introduction to Arduino


Week2

Embedded Systems and Sensing
Introduction to embedded systems and smart sensing.

Sustainable Development and Edge
Working with Sensors (accel, gyro, light etc.)


Week3

Embedded systems and Frameworks. Challenges with TinyML

Tutorial on Computer Architecture

Camera and Vison


Week4


Introduction to ML

Machine Learning Paradigm.

Deep dive for Assignment 1
Teachable Machine Tiny Machine


Week5

AI Lifecycle & ML workflow, ML on Mobile and Edge Devices (ML Ops).

Industry/Research Talk
Building a motion recognition system


Week6
Designing responsible AI for IoT.
Responsible AI: Data ethics, legal social and use cases.

Understanding and framing problems
Responding to voice


Week7
Security and Privacy
Security and privacy in edge computing environments.

Deep dive for Assignment 2
Working on Project Pitch


Week8


St. Patricks Day Holiday

St. Patricks Day Holiday

Sensor Fusion
CW1 [40%]
Project Pitch Presentation












(Tuesday 18th March, Noon)


Week9
Embedded Systems and
Advanced Sensing


Advanced Sensing (Wearable, Mobile, Vision)


Image Classificiation

Adding sight to sensors



Week10

Embedded Hardware/ Software and Challenges
Quantization, Quantization Aware Training (QAT)
Post Training Quantization (PTQ), Quantization Aware Training (QAT), Inference using TF vs. TFLite, Model Conversion and
Deployment. Resource allocation in Edge/ Cloud


Industry/ Research Talk


Object Detection



Week11

The future of Tiny ML/ Federated learning etc.


Personalisation, Federated frameworks, synthetic data, digital twin


Industry/Research Talk


Project Work
CW2 [60%]
Project Presentation and Demo Slides
(Submission Noon, Friday)


Week12


Assessment


Presentations/ demos


Presentations/ demos


Presentations/ demos
CW2 [60%]
Project Presentation and Demo (Presentation
schedule to be confirmed)

Easter





Easter










7. Student Voice and Support Services



As a course team, we incorporate the key partnership principles set out in the joint UU & Ulster University Students' Union Student Voice Guidelines and proactively engage with the democratic election of UUSU academic student reps (Faculty Reps, School/Dept Reps & Course Reps) to ensure that student opinion is heard at Ulster. We respect your views and welcome your honest and constructive feedback on the module.

There are several ways to do this:
You can contact your Module Coordinator about any queries related to your learning experiences on the module as/when you have them.
You can voice your opinions through the formal Staff/Student Consultative Committee process by contacting one of the elected UUSU Course Reps in your class.
You will have the opportunity to give feedback on the module through completing the online Student Module Feedback Survey.


UUSU Advice Bureau Service
If you are experiencing difficulties that are impacting your studies, you can contact the Advice Bureau in the Students' Union (UUSU). You can get advice and guidance on issues such as - complaints, appeals, housing problems, disciplinaries, and info on various support providers available. To have a chat with the team, contact UUSU online.


UU Student Wellbeing Service
Ulster University's Student Wellbeing team is available to help you manage common pressures many students experience while studying in higher education. Common pressures include stress, relationship issues, financial problems, and managing disability-related challenges, including mental health difficulties. There is no stigma to seeking support to maximise your wellbeing and achievement at UU.

Student Wellbeing support is free and confidential and is located on each of our campuses. Please view the Student Wellbeing pages for details on how to contact student wellbeing staff.

Other external helplines are also available.

UU Student Success Team
Ulster University's Student Success team has developed a series of academic and study skills training resources and workshops to help you succeed educationally and develop personally and professionally. You can access these resources via the Student Success website or you can contact the team directly via email.
















































































